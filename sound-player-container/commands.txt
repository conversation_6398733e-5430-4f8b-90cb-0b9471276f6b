# Simple AWOS Audio Player Docker Run Command
# Simplified container that fetches audio from weather API

docker run -d --name simple-awos-player \
  -v /dev:/dev \
  --privileged \
  --restart unless-stopped \
  -e API_BASE_URL=https://awosnew.skytraces.com \
  -e VOLUME=80 \
  -e GPIO_CHIP='gpiochip4' \
  -e GPIO_PIN=18 \
  ghcr.io/devtomsuys/simple-awos-player:latest

# Testing Commands:
# docker logs -f simple-awos-player    # View logs
# docker exec -it simple-awos-player /bin/bash  # Enter container