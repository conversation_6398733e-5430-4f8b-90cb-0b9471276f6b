# Simplified Sound Player Container - Docker Commands
# Fetches AWOS audio from weather API endpoint instead of generating locally

docker run -d --name awos-slim \
  -v /dev:/dev \
  --privileged \
  --restart unless-stopped \
  -e API_BASE_URL=https://awosnew.skytraces.com \
  -e VOLUME=80 \
  -e GPIO_CHIP=gpiochip4 \
  -e GPIO_PIN=18 \
  -e ENABLE_S3_UPLOAD=true \
  -e S3_ACCESS_KEY_ID=c53df2e89e72fe035e7db5a899c9b4de \
  -e S3_SECRET_ACCESS_KEY=e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6 \
  -e S3_ENDPOINT_URL=https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com \
  -e S3_BUCKET_NAME=recordings \
  -e ENABLE_HLS_STREAMING=true \
  -e HLS_VPS_UPLOAD_URL=https://awosnew.skytraces.com \
  -e STATION_NAME=185807 \
  ghcr.io/devtomsuys/awos-slim:20250730-234054

# Testing Commands:
# docker logs -f simplified-sound-player    # View logs
# docker exec -it simplified-sound-player /bin/bash  # Enter container
# docker stop simplified-sound-player       # Stop container
# docker rm simplified-sound-player         # Remove container

# Build Commands:
# docker build -t simplified-sound-player .
# docker tag simplified-sound-player ghcr.io/devtomsuys/simplified-sound-player:latest
# docker push ghcr.io/devtomsuys/simplified-sound-player:latest
