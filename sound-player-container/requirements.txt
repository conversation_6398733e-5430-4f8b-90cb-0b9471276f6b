# Simplified Sound Player Container Requirements
# Only includes dependencies needed for core functionality

# Audio processing and device management
sounddevice>=0.4.6
numpy>=1.24.0
scipy>=1.10.0
soundfile>=0.12.1

# HTTP requests for fetching AWOS audio from API
requests>=2.31.0

# GPIO control for relay management
gpiod>=2.3.0

# S3/Cloud storage for recording uploads
boto3>=1.34.0

# HLS streaming and WebSocket uploads to VPS
ffmpeg-python>=0.2.0
m3u8>=3.5.0
websockets>=11.0.0

# Audio format handling (for temporary file processing)
pydub>=0.25.1

# Logging and utilities
python-dateutil>=2.8.0

pytz