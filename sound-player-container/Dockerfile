# Simplified Sound Player Container
# Fetches AWOS audio from weather API endpoint
FROM python:3.11-slim-bookworm

# Install system dependencies for audio, GPIO, and media processing
RUN apt-get update && apt-get install -y \
    alsa-utils \
    sox \
    libgpiod2 \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create application directory
WORKDIR /app

# Copy requirements and install Python packages
COPY requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY main.py /app/main.py
COPY audio_event_manager.py /app/audio_event_manager.py
COPY unified_signal_detector.py /app/unified_signal_detector.py
COPY recording_controller.py /app/recording_controller.py
COPY awos_controller.py /app/awos_controller.py
COPY audio_config.py /app/audio_config.py
COPY s3_upload_manager.py /app/s3_upload_manager.py
COPY gpio_controller.py /app/gpio_controller.py
COPY hls_audio_subscriber.py /app/hls_audio_subscriber.py
COPY hls_vps_uploader.py /app/hls_vps_uploader.py
COPY audio_device_manager.py /app/audio_device_manager.py

# Create directories for recordings and HLS output
RUN mkdir -p /app/recordings /app/hls

# Set up audio group and user
RUN useradd -r -g audio soundplayer && \
    chown -R soundplayer:audio /app

# Run as the soundplayer user
USER soundplayer

# Run the simplified application
CMD ["python", "/app/main.py"]
