#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test all imports used in the main application."""
    try:
        # Standard library imports
        import asyncio
        import os
        import signal
        import tempfile
        import threading
        import time
        from pathlib import Path
        from typing import Optional
        logger.info("✓ Standard library imports successful")
        
        # Third-party imports
        import requests
        import sounddevice as sd
        import subprocess
        import numpy as np
        import soundfile as sf
        import pytz
        from pydub import AudioSegment
        import boto3
        import websockets
        import ffmpeg
        import m3u8
        logger.info("✓ Third-party imports successful")
        
        # Local module imports
        from audio_event_manager import AudioEventManager, AudioEvent, AudioEventSubscriber
        from unified_signal_detector import UnifiedSignalDetector, SignalEvent
        from recording_controller import RecordingController
        from awos_controller import AWOSController
        from audio_config import AudioConfig
        from s3_upload_manager import S3UploadManager
        from gpio_controller import GpioController
        from hls_audio_subscriber import H<PERSON><PERSON>udioSubscriber
        from hls_vps_uploader import H<PERSON><PERSON>SUploader
        from audio_device_manager import AudioDeviceManager
        logger.info("✓ Local module imports successful")
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ Unexpected error: {e}")
        return False

def test_basic_instantiation():
    """Test basic instantiation of key classes."""
    try:
        # Test AudioConfig
        logger.info(f"Sample rate: {AudioConfig.SAMPLE_RATE}")
        logger.info(f"Channels: {AudioConfig.CHANNELS}")
        
        # Test AudioDeviceManager
        device_manager = AudioDeviceManager()
        logger.info("✓ AudioDeviceManager instantiation successful")
        
        # Test UnifiedSignalDetector
        signal_detector = UnifiedSignalDetector(
            signal_threshold_high=50000,
            signal_threshold_low=10000,
            click_min_duration=0.1
        )
        logger.info("✓ UnifiedSignalDetector instantiation successful")
        
        # Test AWOSController
        awos_controller = AWOSController()
        logger.info("✓ AWOSController instantiation successful")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Instantiation error: {e}")
        return False

def main():
    """Main test function."""
    logger.info("=== Testing Sound Player Container Dependencies ===")
    
    success = True
    
    # Test imports
    logger.info("\n--- Testing Imports ---")
    if not test_imports():
        success = False
    
    # Test basic instantiation
    logger.info("\n--- Testing Basic Instantiation ---")
    if not test_basic_instantiation():
        success = False
    
    # Summary
    logger.info("\n=== Test Summary ===")
    if success:
        logger.info("✓ All tests passed!")
        return 0
    else:
        logger.error("✗ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
