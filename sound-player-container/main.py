#!/usr/bin/env python3
"""
Simplified Sound Player Container - Main Application

This container fetches AWOS audio from the weather API endpoint and handles:
- Audio event management and signal detection
- S3 recording and uploading
- Click detection for AWOS and radio check
- WebSocket HLS file uploading
- GPIO control during transmission
- Sound injection during transmissions

No longer generates AWOS locally - fetches from weather-api-container instead.
"""

import asyncio
import logging
import os
import signal
import sys
import tempfile
import threading
import time
from pathlib import Path
from typing import Optional

import requests
import sounddevice as sd
import subprocess

# Import our modules
from audio_event_manager import AudioEventManager
from unified_signal_detector import UnifiedSignalDetector
from recording_controller import RecordingController
from awos_controller import AWOSController
from audio_config import AudioConfig
from s3_upload_manager import S3UploadManager
from gpio_controller import GpioController
from hls_audio_subscriber import HLSAudioSubscriber
from hls_vps_uploader import HLSVPSUploader
from audio_device_manager import AudioDeviceManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SimplifiedSoundPlayer:
    """Main application class for the simplified sound player container."""
    
    def __init__(self):
        """Initialize the sound player application."""
        self.running = False
        self.shutdown_event = threading.Event()
        
        # Core components
        self.audio_event_manager: Optional[AudioEventManager] = None
        self.signal_detector: Optional[UnifiedSignalDetector] = None
        self.recording_controller: Optional[RecordingController] = None
        self.awos_controller: Optional[AWOSController] = None
        self.s3_upload_manager: Optional[S3UploadManager] = None
        self.gpio_controller: Optional[GpioController] = None
        self.hls_subscriber: Optional[HLSAudioSubscriber] = None
        self.hls_uploader: Optional[HLSVPSUploader] = None
        self.audio_device_manager: Optional[AudioDeviceManager] = None
        
        # Configuration
        self.api_base_url = os.getenv('API_BASE_URL', 'https://awosnew.skytraces.com')
        self.volume = int(os.getenv('VOLUME', '80'))
        self.gpio_chip = os.getenv('GPIO_CHIP', 'gpiochip4')
        self.gpio_pin = int(os.getenv('GPIO_PIN', '18'))
        
        logger.info(f"Simplified Sound Player initialized:")
        logger.info(f"  API Base URL: {self.api_base_url}")
        logger.info(f"  Volume: {self.volume}%")
        logger.info(f"  GPIO: {self.gpio_chip} pin {self.gpio_pin}")
    
    def fetch_awos_audio(self) -> Optional[str]:
        """
        Fetch AWOS audio file from the weather API endpoint.
        
        Returns:
            Path to downloaded audio file, or None if failed
        """
        try:
            awos_url = f"{self.api_base_url}/4FL5/awos.mp3"
            logger.info(f"Fetching AWOS audio from: {awos_url}")
            
            response = requests.get(awos_url, timeout=30)
            response.raise_for_status()
            
            # Save to temporary file
            temp_file = tempfile.NamedTemporaryFile(suffix='.mp3', delete=False)
            temp_file.write(response.content)
            temp_file.close()
            
            logger.info(f"AWOS audio downloaded to: {temp_file.name}")
            return temp_file.name
            
        except Exception as e:
            logger.error(f"Failed to fetch AWOS audio: {e}")
            return None
    
    def play_awos_audio(self, audio_file_path: str) -> None:
        """
        Play AWOS audio through speakers with GPIO control and audio injection.
        
        Args:
            audio_file_path: Path to the audio file to play
        """
        try:
            logger.info("Starting AWOS playback sequence")
            
            # 1. Engage GPIO relay
            if self.gpio_controller:
                self.gpio_controller.engage_relay()
            
            # 2. Suspend click detection during playback
            if self.awos_controller:
                self.awos_controller.suspend_click_detection()
            
            # 3. Start audio injection for recording (parallel thread)
            injection_thread = None
            if self.audio_event_manager:
                injection_thread = threading.Thread(
                    target=self.audio_event_manager.inject_awos_audio_chunks,
                    args=(audio_file_path,),
                    daemon=True
                )
                injection_thread.start()
            
            # 4. Play audio through speakers using sox
            volume_percent = self.volume / 100.0
            cmd = ['sox', audio_file_path, '-d', 'vol', str(volume_percent)]
            
            logger.info(f"Playing AWOS audio with volume {self.volume}%")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Audio playback failed: {result.stderr}")
            else:
                logger.info("AWOS audio playback completed")
            
            # 5. Wait for injection to complete
            if injection_thread:
                injection_thread.join(timeout=30)
            
        except Exception as e:
            logger.error(f"Error during AWOS playback: {e}")
        
        finally:
            # 6. Always cleanup - resume click detection and disengage relay
            try:
                if self.awos_controller:
                    self.awos_controller.resume_click_detection()
                if self.gpio_controller:
                    self.gpio_controller.disengage_relay()
                logger.info("AWOS playback sequence completed")
            except Exception as e:
                logger.error(f"Error during AWOS cleanup: {e}")
    
    def handle_awos_request(self) -> None:
        """Handle AWOS request - fetch and play audio."""
        logger.info("AWOS request received")
        
        # Fetch audio from API
        audio_file = self.fetch_awos_audio()
        if not audio_file:
            logger.error("Failed to fetch AWOS audio - cannot play")
            return
        
        try:
            # Play the audio
            self.play_awos_audio(audio_file)
        finally:
            # Clean up temporary file
            try:
                os.unlink(audio_file)
                logger.debug(f"Cleaned up temporary file: {audio_file}")
            except Exception as e:
                logger.warning(f"Failed to clean up temporary file {audio_file}: {e}")
    
    def handle_radio_check(self) -> None:
        """Handle radio check request."""
        logger.info("Radio check request received")
        # For now, just log - could be extended to play a test tone
        # or perform other radio check functionality
    
    def handle_weather_preparation(self) -> None:
        """Handle weather preparation (3rd click)."""
        logger.info("Weather preparation started (3rd click detected)")
        # This could be used for pre-fetching weather data or other prep work
    
    def initialize_components(self) -> bool:
        """
        Initialize all system components.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            logger.info("Initializing system components...")
            
            # Initialize audio device manager
            self.audio_device_manager = AudioDeviceManager()
            input_device, output_device = self.audio_device_manager.detect_devices()
            if input_device is None:
                logger.error("No suitable audio input device found")
                return False
            
            # Initialize audio event manager
            self.audio_event_manager = AudioEventManager(
                sample_rate=AudioConfig.SAMPLE_RATE,
                channels=AudioConfig.CHANNELS,
                chunk_size=AudioConfig.CHUNK_SIZE,
                freq_min_hz=AudioConfig.FREQ_MIN_HZ,
                freq_max_hz=AudioConfig.FREQ_MAX_HZ
            )
            self.audio_event_manager.set_input_device(input_device)
            
            # Initialize signal detector
            self.signal_detector = UnifiedSignalDetector(
                signal_threshold_high=AudioConfig.SIGNAL_THRESHOLD_HIGH,
                signal_threshold_low=AudioConfig.SIGNAL_THRESHOLD_LOW,
                click_min_duration=AudioConfig.CLICK_MIN_DURATION
            )
            
            # Initialize S3 upload manager first (if enabled)
            if AudioConfig.ENABLE_S3_UPLOAD:
                self.s3_upload_manager = S3UploadManager(
                    access_key_id=AudioConfig.S3_ACCESS_KEY_ID,
                    secret_access_key=AudioConfig.S3_SECRET_ACCESS_KEY,
                    endpoint_url=AudioConfig.S3_ENDPOINT_URL,
                    bucket_name=AudioConfig.S3_BUCKET_NAME,
                    station_name=AudioConfig.STATION_NAME,
                    region_name=AudioConfig.S3_REGION_NAME,
                    max_retry_delay=AudioConfig.S3_MAX_RETRY_DELAY,
                    max_concurrent_uploads=AudioConfig.S3_MAX_CONCURRENT_UPLOADS
                )

            # Initialize recording controller
            if AudioConfig.ENABLE_RECORDING:
                self.recording_controller = RecordingController(
                    storage_path=AudioConfig.RECORDING_STORAGE_PATH,
                    pre_roll_seconds=AudioConfig.PRE_ROLL_SECONDS,
                    post_roll_seconds=AudioConfig.POST_ROLL_SECONDS,
                    min_segment_duration=AudioConfig.MIN_SEGMENT_DURATION,
                    max_segment_duration=AudioConfig.MAX_SEGMENT_DURATION,
                    merge_gap_threshold=AudioConfig.MERGE_GAP_THRESHOLD,
                    s3_upload_manager=self.s3_upload_manager
                )
                self.signal_detector.add_handler(self.recording_controller.on_signal_detected)
                self.audio_event_manager.add_subscriber(self.recording_controller)
            
            # Initialize GPIO controller
            try:
                self.gpio_controller = GpioController(
                    chip_name=self.gpio_chip,
                    pin_number=self.gpio_pin
                )
            except Exception as e:
                logger.warning(f"GPIO controller initialization failed: {e}")
                logger.warning("Continuing without GPIO control")
            
            # Initialize AWOS controller with callbacks
            self.awos_controller = AWOSController(
                awos_callback=self.handle_awos_request,
                weather_prep_callback=self.handle_weather_preparation,
                radio_check_callback=self.handle_radio_check,
                click_min_duration=AudioConfig.CLICK_MIN_DURATION,
                click_max_duration=AudioConfig.CLICK_MAX_DURATION,
                click_cooldown=AudioConfig.CLICK_COOLDOWN,
                awos_click_count=AudioConfig.AWOS_CLICK_COUNT,
                radio_check_click_count=AudioConfig.RADIO_CHECK_CLICK_COUNT
            )
            self.signal_detector.add_handler(self.awos_controller.on_signal_detected)
            self.audio_event_manager.add_subscriber(self.awos_controller)
            
            # Initialize HLS components if enabled
            if AudioConfig.ENABLE_HLS_STREAMING:
                self.hls_subscriber = HLSAudioSubscriber()
                self.audio_event_manager.add_subscriber(self.hls_subscriber)
                
                # Initialize HLS VPS uploader if configured
                if AudioConfig.HLS_VPS_UPLOAD_URL:
                    self.hls_uploader = HLSVPSUploader(
                        upload_url=AudioConfig.HLS_VPS_UPLOAD_URL,
                        auth_token=AudioConfig.HLS_VPS_AUTH_TOKEN,
                        station_id=AudioConfig.STATION_NAME
                    )
                    if self.hls_subscriber:
                        self.hls_subscriber.set_vps_uploader(self.hls_uploader)
            
            # Add signal detector to audio event manager
            self.audio_event_manager.add_subscriber(self.signal_detector)
            
            logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Component initialization failed: {e}")
            return False
    
    def start(self) -> None:
        """Start the sound player application."""
        if self.running:
            logger.warning("Application is already running")
            return
        
        logger.info("Starting Simplified Sound Player...")
        
        # Initialize components
        if not self.initialize_components():
            logger.error("Failed to initialize components")
            return
        
        # Start audio processing
        try:
            self.audio_event_manager.start()
            self.running = True
            logger.info("Sound player started successfully")
            
            # Main loop - wait for shutdown
            while self.running and not self.shutdown_event.is_set():
                time.sleep(1)
                
        except Exception as e:
            logger.error(f"Error during operation: {e}")
        finally:
            self.stop()
    
    def stop(self) -> None:
        """Stop the sound player application."""
        if not self.running:
            return
        
        logger.info("Stopping Simplified Sound Player...")
        self.running = False
        self.shutdown_event.set()
        
        # Stop components
        try:
            if self.audio_event_manager:
                self.audio_event_manager.stop()
            if self.s3_upload_manager:
                self.s3_upload_manager.stop()
            if self.gpio_controller:
                self.gpio_controller.cleanup()
            if self.hls_uploader:
                self.hls_uploader.stop()
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        
        logger.info("Sound player stopped")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()


def main():
    """Main entry point."""
    logger.info("=== Simplified Sound Player Container ===")
    logger.info("Fetches AWOS audio from weather API endpoint")
    
    # Create application instance
    app = SimplifiedSoundPlayer()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, app.signal_handler)
    signal.signal(signal.SIGTERM, app.signal_handler)
    
    try:
        # Start the application
        app.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Application error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
