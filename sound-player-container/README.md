# Simplified Sound Player Container

A simplified Docker container that fetches AWOS audio from the weather API endpoint and handles audio processing, recording, and transmission control.

## Overview

This container has been simplified to follow a single source of truth architecture. Instead of generating AWOS messages locally, it fetches pre-generated audio from the weather-api-container endpoint.

## Core Functionality

### Preserved Features
- **Audio Event Manager**: Manages real-time audio processing and event distribution
- **Signal Detector**: Detects audio signals and click patterns
- **S3 Recording & Upload**: Records audio segments and uploads to cloud storage
- **Click Detection**: Detects 3-click AWOS requests and 4-click radio checks
- **WebSocket HLS Upload**: Uploads HLS segments to VPS via WebSocket
- **GPIO Control**: Controls relay during transmission
- **Sound Injection**: Injects audio into recording pipeline during playback

### Removed Features
- Local AWOS generation (now fetches from API)
- HLS server functionality (no longer serves /player page)
- Audio component downloading and TTS generation
- Weather data fetching and processing

## Environment Variables

### Required
- `API_BASE_URL`: Base URL for weather API (default: https://awosnew.skytraces.com)
- `VOLUME`: Audio playback volume 0-100 (default: 80)
- `GPIO_CHIP`: GPIO chip name (default: gpiochip4)
- `GPIO_PIN`: GPIO pin number (default: 18)

### Optional - S3 Recording Upload
- `ENABLE_S3_UPLOAD`: Enable S3 uploads (default: false)
- `S3_ACCESS_KEY_ID`: S3 access key
- `S3_SECRET_ACCESS_KEY`: S3 secret key
- `S3_ENDPOINT_URL`: S3 endpoint URL
- `S3_BUCKET_NAME`: S3 bucket name (default: recordings)
- `STATION_NAME`: Station identifier for uploads

### Optional - HLS WebSocket Upload
- `ENABLE_HLS_STREAMING`: Enable HLS streaming (default: false)
- `HLS_VPS_UPLOAD_URL`: WebSocket URL for HLS uploads
- `HLS_VPS_AUTH_TOKEN`: Authentication token for VPS
- `STATION_NAME`: Station identifier

## Docker Usage

### Basic Usage
```bash
docker run -d --name simplified-sound-player \
  -v /dev:/dev \
  --privileged \
  --restart unless-stopped \
  -e API_BASE_URL=https://awosnew.skytraces.com \
  -e VOLUME=80 \
  -e GPIO_CHIP=gpiochip4 \
  -e GPIO_PIN=18 \
  ghcr.io/devtomsuys/simplified-sound-player:latest
```

### With S3 Upload
```bash
docker run -d --name simplified-sound-player \
  -v /dev:/dev \
  --privileged \
  --restart unless-stopped \
  -e API_BASE_URL=https://awosnew.skytraces.com \
  -e VOLUME=80 \
  -e GPIO_CHIP=gpiochip4 \
  -e GPIO_PIN=18 \
  -e ENABLE_S3_UPLOAD=true \
  -e S3_ACCESS_KEY_ID=your_key \
  -e S3_SECRET_ACCESS_KEY=your_secret \
  -e S3_ENDPOINT_URL=your_endpoint \
  -e S3_BUCKET_NAME=recordings \
  -e STATION_NAME=RIDGE_LANDING \
  ghcr.io/devtomsuys/simplified-sound-player:latest
```

## Operation

1. **Click Detection**: Listens for microphone click patterns
   - 3 clicks: Triggers AWOS request
   - 4 clicks: Triggers radio check

2. **AWOS Playback**: When 3 clicks detected:
   - Fetches latest AWOS audio from `{API_BASE_URL}/4FL5/awos.mp3`
   - Engages GPIO relay
   - Suspends click detection during playback
   - Plays audio through speakers
   - Injects audio into recording pipeline
   - Resumes click detection after completion

3. **Recording**: Continuously records audio when signals detected
   - Pre-roll and post-roll buffering
   - Automatic segment merging
   - S3 upload if configured

4. **HLS Streaming**: If enabled, creates HLS segments and uploads to VPS

## Architecture

The container follows a simplified architecture:
- Fetches AWOS audio from weather API (single source of truth)
- Preserves all core audio processing functionality
- Removes duplicate AWOS generation logic
- No longer serves web interface (handled by VPS)

## Dependencies

- Python 3.11
- Audio libraries: sounddevice, numpy, scipy, soundfile
- HTTP client: requests
- GPIO control: gpiod
- Cloud storage: boto3
- Media processing: ffmpeg, pydub
- WebSocket: websockets

## Building

```bash
docker build -t simplified-sound-player .
docker tag simplified-sound-player ghcr.io/devtomsuys/simplified-sound-player:latest
docker push ghcr.io/devtomsuys/simplified-sound-player:latest
```
